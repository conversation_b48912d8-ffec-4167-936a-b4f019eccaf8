/* Task List Panel Styles */

/* Task List Panel Overlay */
.task-list-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1000;
  pointer-events: none;
  transition: background-color 0.15s ease-out, backdrop-filter 0.15s ease-out;
}

.task-list-panel:not(.hidden) {
  background: var(--bg-overlay);
  backdrop-filter: blur(4px);
  pointer-events: all;
}

.task-list-panel.hidden {
  pointer-events: none;
}

/* Task List Content Container */
.task-list-content {
  background: var(--bg-primary);
  box-shadow: var(--shadow-xl);
  width: 400px;
  height: 100vh;
  overflow-y: auto;
  margin-right: auto;
  transform: translateX(-100%);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: all;
  will-change: transform;
}

.task-list-panel:not(.hidden) .task-list-content {
  transform: translateX(0);
}

/* Task List Header */
.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--bg-primary);
  z-index: 10;
}

.task-list-header h2 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Task List Sections */
.task-list-sections {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.task-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.task-section h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

/* Add Task Form */
.add-task-form {
  margin-bottom: var(--spacing-md);
}

.task-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.task-input-group input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.task-input-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.task-input-group input::placeholder {
  color: var(--text-secondary);
}

.add-task-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-task-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.add-task-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-task-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Task List */
.task-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  min-height: 60px;
  padding: var(--spacing-sm);
  border: 2px dashed var(--border-color);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

.task-list.drag-over {
  border-color: var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.05);
}

.task-list:empty::before {
  content: "No tasks yet";
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: var(--spacing-md);
  display: block;
}

#add-task-list:empty::before {
  content: "Add your first task above";
}

#progress-task-list:empty::before {
  content: "No tasks in progress";
}

#archived-task-list:empty::before {
  content: "No archived tasks";
}

/* Task Item */
.task-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  cursor: grab;
  transition: all var(--transition-fast);
  user-select: none;
}

.task-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  cursor: grabbing;
  z-index: 1000;
}

.task-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.task-item-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
  flex: 1;
  margin-right: var(--spacing-sm);
}

.task-item-category {
  background: var(--primary-color);
  color: white;
  padding: 2px var(--spacing-xs);
  border-radius: 12px;
  font-size: var(--font-size-xs);
  font-weight: 500;
  white-space: nowrap;
}

.task-item-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.task-action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.task-action-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.task-action-btn.complete {
  border-color: var(--success-color);
  color: var(--success-color);
}

.task-action-btn.complete:hover {
  background: var(--success-color);
  color: white;
}

.task-action-btn.cancel {
  border-color: var(--error-color);
  color: var(--error-color);
}

.task-action-btn.cancel:hover {
  background: var(--error-color);
  color: white;
}

.task-action-btn.restore {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.task-action-btn.restore:hover {
  background: var(--primary-color);
  color: white;
}

.task-action-btn.delete {
  border-color: var(--error-color);
  color: var(--error-color);
}

.task-action-btn.delete:hover {
  background: var(--error-color);
  color: white;
}

/* Task List Button in Header */
.task-list-btn {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-list-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: scale(1.1);
}

.task-list-btn:active {
  transform: scale(0.95);
}

/* Animations */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Responsive Design for Task List */
@media (max-width: 768px) {
  .task-list-content {
    width: 350px;
  }

  .task-list-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  }

  .task-list-sections {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
    gap: var(--spacing-lg);
  }

  .task-item {
    padding: var(--spacing-sm);
  }

  .task-item-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .task-list-content {
    width: 300px;
  }
}
